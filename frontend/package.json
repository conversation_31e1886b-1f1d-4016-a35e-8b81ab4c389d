{"name": "paper-downloader-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.446.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7", "axios": "^1.7.7", "react-hook-form": "^7.53.0", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "react-intersection-observer": "^9.13.1", "framer-motion": "^11.9.0", "next-themes": "^0.3.0"}, "devDependencies": {"typescript": "^5.6.2", "@types/node": "^22.7.4", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "eslint": "^8.57.1", "eslint-config-next": "^15.3.3", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0"}, "engines": {"node": ">=18.0.0"}}