'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Clock, X, Trash2, Search, TrendingUp } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { SearchHistoryItem } from '@/lib/utils/storage'
import { PaperSource } from '@/lib/types'

interface SearchHistoryDropdownProps {
  searchHistory: SearchHistoryItem[]
  isOpen: boolean
  onClose: () => void
  onSelectSearch: (query: string, sources: PaperSource[]) => void
  onRemoveSearch: (searchId: string) => void
  onClearHistory: () => void
  className?: string
}

const SOURCE_COLORS: Record<PaperSource, string> = {
  [PaperSource.ARXIV]: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  [PaperSource.CROSSREF]: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  [PaperSource.ASM]: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  [PaperSource.GOOGLE_SCHOLAR]: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  [PaperSource.IEEE]: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
  [PaperSource.PUBMED]: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  [PaperSource.SCIHUB]: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
}

const formatTimeAgo = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  if (days < 7) return `${days}d ago`
  return new Date(timestamp).toLocaleDateString()
}

export function SearchHistoryDropdown({
  searchHistory,
  isOpen,
  onClose,
  onSelectSearch,
  onRemoveSearch,
  onClearHistory,
  className,
}: SearchHistoryDropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  const recentSearches = searchHistory.slice(0, 8) // Show max 8 recent searches
  const hasHistory = recentSearches.length > 0

  return (
    <div
      ref={dropdownRef}
      className={cn(
        'absolute top-full left-0 right-0 z-50 mt-2',
        className
      )}
    >
      <Card className="shadow-lg border-2 bg-white dark:bg-gray-900 max-h-96 overflow-hidden">
        {hasHistory ? (
          <>
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">Recent Searches</span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearHistory}
                  className="text-muted-foreground hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-muted-foreground"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Search History List */}
            <div className="max-h-80 overflow-y-auto">
              {recentSearches.map((item, index) => (
                <div
                  key={item.id}
                  className="group flex items-center gap-3 p-3 hover:bg-muted/50 cursor-pointer transition-colors border-b last:border-b-0"
                  onClick={() => onSelectSearch(item.query, item.sources)}
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Search className="w-4 h-4 text-primary" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-sm font-medium truncate">{item.query}</p>
                      {item.resultsCount !== undefined && (
                        <Badge variant="secondary" className="text-xs">
                          {item.resultsCount} results
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(item.timestamp)}
                      </span>
                      <Separator orientation="vertical" className="h-3" />
                      <div className="flex gap-1">
                        {item.sources.slice(0, 3).map((source) => (
                          <Badge
                            key={source}
                            variant="outline"
                            className={cn(
                              'text-xs px-1.5 py-0.5',
                              SOURCE_COLORS[source]
                            )}
                          >
                            {source.toUpperCase()}
                          </Badge>
                        ))}
                        {item.sources.length > 3 && (
                          <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                            +{item.sources.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onRemoveSearch(item.id)
                    }}
                    className="opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Footer */}
            {searchHistory.length > 8 && (
              <div className="p-3 border-t bg-muted/30">
                <p className="text-xs text-muted-foreground text-center">
                  Showing {recentSearches.length} of {searchHistory.length} recent searches
                </p>
              </div>
            )}
          </>
        ) : (
          /* Empty State */
          <div className="p-8 text-center">
            <div className="w-12 h-12 rounded-full bg-muted/50 flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="w-6 h-6 text-muted-foreground" />
            </div>
            <h3 className="text-sm font-medium mb-1">No search history</h3>
            <p className="text-xs text-muted-foreground">
              Your recent searches will appear here
            </p>
          </div>
        )}
      </Card>
    </div>
  )
}
