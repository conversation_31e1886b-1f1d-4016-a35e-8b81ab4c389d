'use client'

import { useSearch } from '@/lib/hooks/useSearch'
import { But<PERSON> } from '@/components/ui/button'
import { SearchFormSchema } from '@/lib/hooks/useSearch'

export const SearchHistory = () => {
  const { searchHistory, performSearch } = useSearch()

  if (searchHistory.length === 0) {
    return null
  }

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Recent Searches</h3>
      <div className="flex flex-wrap gap-2">
        {searchHistory.map((search: SearchFormSchema, index: number) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => performSearch(search)}
          >
            {search.query}
          </Button>
        ))}
      </div>
    </div>
  )
} 