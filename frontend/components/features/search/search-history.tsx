'use client'

import { useSearch } from '@/lib/hooks/useSearch'
import { Button } from '@/components/ui/button'
import { SearchHistoryItem } from '@/lib/utils/storage'

export const SearchHistory = () => {
  const { searchHistory, performSearch } = useSearch()

  if (searchHistory.length === 0) {
    return null
  }

  const handleSearchClick = (item: SearchHistoryItem) => {
    performSearch({
      query: item.query,
      sources: item.sources,
      maxResults: 20, // Default value
    })
  }

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Recent Searches</h3>
      <div className="flex flex-wrap gap-2">
        {searchHistory.map((item: SearchHistoryItem, index: number) => (
          <Button
            key={item.id}
            variant="outline"
            size="sm"
            onClick={() => handleSearchClick(item)}
          >
            {item.query}
          </Button>
        ))}
      </div>
    </div>
  )
}