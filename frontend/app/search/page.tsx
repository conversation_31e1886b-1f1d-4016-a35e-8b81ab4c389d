'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Search, Download, ExternalLink, Calendar, Users, CheckCircle, Eye } from 'lucide-react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MinimalProgress } from '@/components/ui/minimal-progress'
import { PaperViewer } from '@/components/ui/paper-viewer'
import { SourceFilter } from '@/components/features/search/source-filter'
import { EnhancedSearchInput } from '@/components/features/search/enhanced-search-input'
import { useSearch } from '@/lib/hooks/useSearch'
import { useDownloads } from '@/lib/hooks/useDownloads'
import { PaperDTO, PaperSource, DownloadStatus } from '@/lib/types'
import { formatDate } from '@/lib/utils'

export default function SearchPage() {
  const router = useRouter()
  const {
    isSearching,
    searchResults,
    searchError,
    performSearch,
    hasResults,
    getTotalResults,
    searchHistory,
    removeFromHistory,
    clearSearchHistory,
    preferredSources,
    updatePreferredSources
  } = useSearch()
  
  const { 
    createDownloadTask, 
    downloadSinglePaper, 
    isCreatingTask, 
    activeDownloads,
    totalActiveDownloads,
    globalProgress,
    downloadTasks,
    singleDownloads
  } = useDownloads()
  
  const [selectedPapers, setSelectedPapers] = useState<Set<string>>(new Set())
  const [downloadingPapers, setDownloadingPapers] = useState<Set<string>>(new Set())
  const [selectedSources, setSelectedSources] = useState<PaperSource[]>(preferredSources)
  const [searchQuery, setSearchQuery] = useState('')
  const [viewerState, setViewerState] = useState<{
    isOpen: boolean
    pdfUrl: string
    paperTitle: string
    paperUrl?: string
  }>({
    isOpen: false,
    pdfUrl: '',
    paperTitle: '',
    paperUrl: ''
  })

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    if (selectedSources.length === 0) {
      console.warn('No sources selected for search')
      return
    }

    try {
      await performSearch({
        query: searchQuery,
        sources: selectedSources,
        maxResults: 20,
      })
    } catch (error) {
      console.error('Search failed:', error)
      // Error is already handled in useSearch hook
    }
  }

  const handleSourceChange = (sources: PaperSource[]) => {
    setSelectedSources(sources)
    updatePreferredSources(sources)
  }

  const handleHistorySelect = (query: string, sources: PaperSource[]) => {
    setSearchQuery(query)
    setSelectedSources(sources)
    updatePreferredSources(sources)
  }

  const handlePaperSelect = (paperId: string) => {
    const newSelected = new Set(selectedPapers)
    if (newSelected.has(paperId)) {
      newSelected.delete(paperId)
    } else {
      newSelected.add(paperId)
    }
    setSelectedPapers(newSelected)
  }

  const handleBulkDownload = async () => {
    if (selectedPapers.size === 0) return
    
    const paperIds = Array.from(selectedPapers)
    const selectedPapersData = searchResults?.papers.filter(paper => selectedPapers.has(paper.id)) || []
    await createDownloadTask(paperIds, selectedPapersData)
    setSelectedPapers(new Set()) // Clear selection after download starts
  }

  const handleSingleDownload = async (paper: PaperDTO) => {
    setDownloadingPapers(prev => new Set(prev).add(paper.id))
    try {
      await downloadSinglePaper(paper)
    } catch (error) {
      console.error('Failed to download paper:', error)
    } finally {
      setDownloadingPapers(prev => {
        const newSet = new Set(prev)
        newSet.delete(paper.id)
        return newSet
      })
    }
  }

  const handleViewPaper = (paper: PaperDTO) => {
    if (paper.pdf_url) {
      setViewerState({
        isOpen: true,
        pdfUrl: paper.pdf_url,
        paperTitle: paper.title,
        paperUrl: paper.url
      })
    }
  }

  const handleShowDownloads = () => {
    router.push('/downloads')
  }

  // Check if paper is downloaded
  const isPaperDownloaded = (paperId: string): boolean => {
    // Check in completed download tasks containing this paper
    const isInTasks = downloadTasks.some(task =>
      task.status === DownloadStatus.COMPLETED &&
      task.download_urls &&
      task.download_urls.length > 0 &&
      task.paper_ids.includes(paperId)
    )
    
    // Check in single downloads
    const isInSingleDownloads = singleDownloads.some(download =>
      download.paper_id === paperId &&
      download.status === DownloadStatus.COMPLETED
    )
    
    return isInTasks || isInSingleDownloads
  }

  const PaperCard = ({ paper }: { paper: PaperDTO }) => {
    const isDownloading = downloadingPapers.has(paper.id)
    const isSelected = selectedPapers.has(paper.id)
    const isDownloaded = isPaperDownloaded(paper.id)
    
    return (
      <Card className="paper-card">
        <CardHeader>
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg line-clamp-2 flex-1 mr-4">
              {paper.title}
            </CardTitle>
            <div className="flex items-center gap-2 flex-shrink-0">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => handlePaperSelect(paper.id)}
                className="mt-1"
              />
              {paper.pdf_url && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewPaper(paper)}
                  title="View PDF"
                  className="h-8 w-8 p-0"
                >
                  <Eye className="w-3 h-3" />
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSingleDownload(paper)}
                disabled={isDownloading || !paper.pdf_url}
                title={
                  isDownloaded 
                    ? "Already downloaded" 
                    : paper.pdf_url 
                      ? "Download this paper" 
                      : "PDF not available"
                }
                className="h-8 w-8 p-0"
              >
                {isDownloading ? (
                  <div className="spinner w-3 h-3" />
                ) : isDownloaded ? (
                  <CheckCircle className="w-3 h-3 text-green-600" />
                ) : (
                  <Download className="w-3 h-3" />
                )}
              </Button>
            </div>
          </div>
          <CardDescription className="flex items-center gap-2 text-sm">
            <Users className="w-4 h-4" />
            {paper.authors.slice(0, 3).map(author => author.name).join(', ')}
            {paper.authors.length > 3 && ` +${paper.authors.length - 3} more`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
            {paper.abstract}
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              {paper.published_date && (
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {formatDate(paper.published_date)}
                </span>
              )}
              <span className="capitalize bg-secondary px-2 py-1 rounded flex items-center gap-1">
                <Image
                  src={`/logos/${paper.source}.svg`}
                  alt={paper.source}
                  width={12}
                  height={12}
                  className="inline"
                />
                {paper.source.replace('_', ' ')}
              </span>
              {paper.citation_count && (
                <span>{paper.citation_count} citations</span>
              )}
            </div>
            <div className="flex gap-2">
              {paper.url && (
                <Button variant="outline" size="sm" asChild>
                  <a href={paper.url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="w-4 h-4" />
                  </a>
                </Button>
              )}
              {paper.pdf_url && (
                <Button variant="outline" size="sm" asChild>
                  <a href={paper.pdf_url} target="_blank" rel="noopener noreferrer">
                    PDF
                  </a>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Search Research Papers</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Search across multiple academic databases including arXiv, Crossref, Google Scholar, IEEE, and more.
          Download papers individually or in bulk with real-time progress tracking.
        </p>
      </div>

      {/* Search Form */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <EnhancedSearchInput
                value={searchQuery}
                onChange={setSearchQuery}
                onSubmit={handleSearch}
                disabled={isSearching}
                searchHistory={searchHistory}
                onSelectFromHistory={handleHistorySelect}
                onRemoveFromHistory={removeFromHistory}
                onClearHistory={clearSearchHistory}
                autoFocus={true}
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={isSearching || selectedSources.length === 0 || !searchQuery.trim()}
              size="lg"
              className="h-12 px-8"
              title={selectedSources.length === 0 ? "Please select at least one source" : "Search"}
            >
              {isSearching ? (
                <div className="spinner mr-2" />
              ) : (
                <Search className="w-5 h-5 mr-2" />
              )}
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Source Filter */}
      <SourceFilter
        selectedSources={selectedSources}
        onSourceChange={handleSourceChange}
        disabled={isSearching}
      />

      {/* Results Actions */}
      {hasResults() && (
        <div className="flex items-center justify-between bg-muted/50 p-4 rounded-lg">
          <div className="text-sm text-muted-foreground">
            Found {getTotalResults()} papers • {selectedPapers.size} selected
          </div>
          <div className="flex gap-2">
            {totalActiveDownloads > 0 && (
              <Button
                variant="outline"
                onClick={handleShowDownloads}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                View Downloads ({totalActiveDownloads})
              </Button>
            )}
            <Button
              onClick={handleBulkDownload}
              disabled={selectedPapers.size === 0 || isCreatingTask}
              variant="default"
            >
              {isCreatingTask ? (
                <div className="spinner mr-2" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Download Selected ({selectedPapers.size})
            </Button>
          </div>
        </div>
      )}

      {/* Search Error */}
      {searchError && (
        <Card className="border-destructive">
          <CardContent className="p-4">
            <p className="text-destructive">Error: {searchError}</p>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {searchResults && (
        <div className="space-y-4">
          {searchResults.papers.map((paper) => (
            <PaperCard key={paper.id} paper={paper} />
          ))}
          
          {searchResults.papers.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">
                  No papers found. Try adjusting your search terms.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Minimal Progress Bar - Fixed position at bottom right */}
      <MinimalProgress
        totalActiveDownloads={totalActiveDownloads}
        globalProgress={globalProgress}
        activeDownloads={activeDownloads}
        onShowDetails={handleShowDownloads}
      />

      {/* Paper Viewer */}
      {viewerState.isOpen && (
        <PaperViewer
          isOpen={viewerState.isOpen}
          onClose={() => setViewerState({ ...viewerState, isOpen: false })}
          pdfUrl={viewerState.pdfUrl}
          paperTitle={viewerState.paperTitle}
          paperUrl={viewerState.paperUrl}
        />
      )}
    </div>
  )
}
